#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// AD603控制函数声明
void DAC_Init_AD603(void);
void AD603_SetGain(float gain_db);
float get_frequency_correction_factor(float frequency);
float calculate_target_ad603_output(float frequency);
float measure_adc_amplitude(void);
void auto_adjust_gain(void);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;



extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

// AD603增益控制变量
float current_ad603_gain = 0.0f;  // 当前基础增益值(dB)
uint8_t gain_changed = 0;         // 增益改变标志

// 频率校正系数数组 (100Hz到3000Hz，步进100Hz)
// AD603输出幅度 × 3.9 × 校正系数 = 1V
const float freq_correction_factors[30] = {
    4.615, 4.462, 4.325, 4.058, 3.727,
    3.448, 3.175, 2.924, 2.686, 2.475,
    2.278, 2.117, 1.977, 1.876, 1.764,
    1.670, 1.574, 1.482, 1.398, 1.324,
    1.255, 1.192, 1.133, 1.073, 1.024,
    0.974, 0.9266, 0.8827, 0.8415, 0.825
};

// 闭环控制变量
float target_voltage = 1.0f;      // 目标电压 1V
float current_adc_voltage = 0.0f; // 当前ADC检测电压
uint8_t auto_gain_enabled = 1;    // 自动增益控制使能
float gain_adjustment_step = 0.1f; // 增益调节步长



// 增益控制按钮结构
typedef struct {
    char text[10];
    float gain_step;
    uint8_t is_linear;  // 0=dB模式, 1=线性模式
} GainButton;

// 精细dB增益控制按钮数组
GainButton gain_buttons[4] = {
    {"+10dB", 10.0f, 0},    // 粗调：+10dB
    {"+1dB", 1.0f, 0},      // 中调：+1dB
    {"+0.1dB", 0.1f, 0},    // 细调：+0.1dB
    {"+0.01dB", 0.01f, 0}   // 精调：+0.01dB
};

uint8_t selected_gain_button = 0;  // 当前选中的增益按钮
uint8_t gain_mode = 0;             // 0=频率模式, 1=增益模式
uint32_t key1_press_time = 0;      // KEY1按下时间计数器

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始

char lcd_buffer[50];

// DAC初始化函数
void DAC_Init_AD603(void) {
    DAC_InitTypeDef DAC_InitStructure;
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能DAC和GPIOA时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);

    // 配置PA4为DAC输出 (DAC_OUT1)
    // 注意：已禁用ADC2以避免引脚冲突
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // DAC配置
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_None;
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);

    // 使能DAC通道1
    DAC_Cmd(DAC_Channel_1, ENABLE);

    // 设置初始增益为0dB
    AD603_SetGain(0.0f);
}

// AD603增益控制函数
void AD603_SetGain(float gain_db) {
    // AD603增益范围：-11dB到+31dB (42dB范围)
    // 控制电压：0V到1V

    // 限制增益范围
    if (gain_db < -11.0f) gain_db = -11.0f;
    if (gain_db > 31.0f) gain_db = 31.0f;

    // 计算控制电压：(gain_db + 11) / 42 * 1V
    float control_voltage = (gain_db + 11.0f) / 42.0f;

    // 修正：AD603需要0V到1V，但DAC输出0V到3.3V
    // 需要限制控制电压不超过1V
    if (control_voltage > 1.0f) control_voltage = 1.0f;

    // 转换为DAC数值 (12位DAC，参考电压3.3V)
    // DAC输出 = dac_value * 3.3V / 4095
    // 要输出control_voltage，需要：dac_value = control_voltage * 4095 / 3.3
    uint16_t dac_value = (uint16_t)(control_voltage * 4095.0f / 3.3f);

    // 输出到DAC通道1
    DAC_SetChannel1Data(DAC_Align_12b_R, dac_value);
}

// 获取频率校正系数
float get_frequency_correction_factor(float frequency) {
    // 频率范围：100Hz到3000Hz，步进100Hz
    if (frequency < 100.0f) return freq_correction_factors[0];  // 小于100Hz用100Hz的系数
    if (frequency > 3000.0f) return freq_correction_factors[29]; // 大于3000Hz用3000Hz的系数

    // 计算数组索引 (frequency = n*100, n从1开始)
    int index = (int)(frequency / 100.0f) - 1;
    if (index < 0) index = 0;
    if (index > 29) index = 29;

    return freq_correction_factors[index];
}

// 计算目标AD603输出幅度
// 目标：AD603输出 × 3.9 × 校正系数 = 1V
// 所以：AD603输出 = 1V / (3.9 × 校正系数)
float calculate_target_ad603_output(float frequency) {
    float correction_factor = get_frequency_correction_factor(frequency);
    return target_voltage / (3.9f * correction_factor);
}

// 测量ADC幅度（使用简化方法）
float measure_adc_amplitude(void) {
    // 使用ADC直接读取方式
    ADC_SoftwareStartConv(ADC1);  // 启动ADC1转换

    // 等待转换完成
    while(!ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC));

    // 读取ADC1值并转换为电压
    uint16_t adc_value = ADC_GetConversionValue(ADC1);
    float voltage = (float)adc_value / 4096.0f * 3.3f;   // 12位ADC，3.3V参考

    // 去除直流偏置（假设信号以1.65V为中心）
    float ac_voltage = fabsf(voltage - 1.65f);

    // 简化：假设这是峰值的一个采样点，乘以估算系数
    // 实际应用中可以使用DMA缓冲区数据进行更精确的计算
    float amplitude = ac_voltage * 1.414f;  // 估算峰值

    return amplitude;
}

// 自动增益调节函数
void auto_adjust_gain(void) {
    if (!auto_gain_enabled) return;

    // 测量当前AD603输出幅度
    current_adc_voltage = measure_adc_amplitude();

    // 计算目标AD603输出幅度
    float target_ad603_output = calculate_target_ad603_output(current_frequency);

    // 计算误差
    float error = target_ad603_output - current_adc_voltage;
    float error_percent = fabsf(error) / target_ad603_output * 100.0f;

    // 如果误差小于5%，认为已经达到目标
    if (error_percent < 5.0f) {
        return;  // 不需要调节
    }

    // 根据误差大小决定调节步长
    float step = gain_adjustment_step;
    if (error_percent > 20.0f) {
        step = 1.0f;    // 大误差用大步长
    } else if (error_percent > 10.0f) {
        step = 0.5f;    // 中等误差用中等步长
    }

    // 根据误差方向调节增益
    if (error > 0) {
        // 当前输出太小，需要增加增益
        current_ad603_gain += step;
    } else {
        // 当前输出太大，需要减少增益
        current_ad603_gain -= step;
    }

    // 限制增益范围
    if (current_ad603_gain < -30.0f) current_ad603_gain = -30.0f;
    if (current_ad603_gain > 30.0f) current_ad603_gain = 30.0f;

    // 应用新的增益设置
    AD603_SetGain(current_ad603_gain);

    // 标记显示需要更新
    gain_changed = 1;
}

// 绘制增益控制按钮
void draw_gain_button(GainButton* btn, uint16_t x, uint16_t y, uint8_t selected, uint8_t pressed) {
    uint16_t bg_color = WHITE;
    uint16_t text_color = BLACK;
    uint16_t border_color = BLACK;

    // 如果不在增益模式，按钮显示为灰色（不可用状态）
    if (!gain_mode) {
        bg_color = LGRAY;
        text_color = GRAY;
        border_color = GRAY;
    } else if (pressed) {
        bg_color = GRAY;
        text_color = WHITE;
    } else if (selected) {
        bg_color = LIGHTBLUE;
        border_color = BLUE;
    }

    // 绘制按钮背景
    lcd_fill(x, y, x + 60, y + 25, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(x, y, x + 60, y + 25, border_color);

    // 绘制按钮文字
    uint16_t text_x = x + (60 - strlen(btn->text) * 6) / 2;
    uint16_t text_y = y + 8;
    lcd_show_string(text_x, text_y, 60, 16, 12, btn->text, text_color);
}

// 绘制所有增益按钮
void draw_all_gain_buttons(uint8_t selected) {
    for (int i = 0; i < 4; i++) {
        uint16_t x = 10 + i * 70;
        uint16_t y = 250;
        draw_gain_button(&gain_buttons[i], x, y, (i == selected), 0);
    }
}

// 精细dB增益调整函数
void adjust_ad603_gain(float step_db) {
    current_ad603_gain += step_db;

    // 限制增益范围
    if (current_ad603_gain < -30.0f) current_ad603_gain = -30.0f;
    if (current_ad603_gain > 30.0f) current_ad603_gain = 30.0f;

    // 直接设置AD603增益
    AD603_SetGain(current_ad603_gain);

    // 标记增益已改变
    gain_changed = 1;
}



// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 其他控制变量
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t selected_button = 0;      // 当前选中的按钮索引

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义四个按钮 - 更大尺寸便于操作
Button_t buttons[4] = {
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED}
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 4; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 4; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 频率改变后，延时一段时间让信号稳定，然后自动调节增益
    delay_ms(100);  // 等待频率稳定
    auto_adjust_gain();  // 自动调节增益以达到目标幅度

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);
    LED_Init();
    Adc_Init();
    // Adc2_Init();  // 禁用ADC2，PA4改为DAC使用
    Adc3_Init();
    DMA1_Init();
    // DMA2_Init();  // 禁用ADC2对应的DMA
    DMA3_Init();
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键
    DAC_Init_AD603();  // 初始化DAC控制AD603

    lcd_init();
   
    sampfre = 409756;

    TIM3_Int_Init(5 - 1, 20 - 1);
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 显示模式切换提示
    lcd_show_string(10, 190, lcddev.width, 20, 12, "Mode: Frequency Control", BLACK);
    lcd_show_string(10, 210, lcddev.width, 20, 12, "Press PE4+PE3 together to switch to Gain mode", BLUE);

    // 绘制增益控制按钮（始终显示，但根据模式调整颜色）
    draw_all_gain_buttons(selected_gain_button);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置初始AD603增益
    AD603_SetGain(current_ad603_gain);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 显示初始增益值和放大倍数
    float initial_voltage_gain = powf(10.0f, current_ad603_gain / 20.0f);
    sprintf(lcd_buffer, "Gain: %.1fdB (x%.2f)", current_ad603_gain, initial_voltage_gain);
    lcd_show_string(10, 280, lcddev.width, 20, 12, lcd_buffer, RED);

    // 显示初始控制电压
    float initial_control_voltage = (current_ad603_gain + 11.0f) / 42.0f;
    sprintf(lcd_buffer, "Control Voltage: %.3fV", initial_control_voltage);
    lcd_show_string(10, 300, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测模式切换（同时按下KEY0和KEY1）
        static uint8_t mode_switch_pressed = 0;
        if (KEY0 == 0 && KEY1 == 0) {
            if (mode_switch_pressed == 0) {
                mode_switch_pressed = 1;

                // 切换模式
                gain_mode = !gain_mode;

                // 清除按钮区域
                lcd_fill(0, 100, lcddev.width, 280, WHITE);

                if (gain_mode) {
                    // 切换到增益模式
                    lcd_show_string(10, 190, lcddev.width, 20, 12, "Mode: AD603 Gain Control", BLACK);
                    draw_all_gain_buttons(selected_gain_button);

                    // 显示当前选中的增益按钮信息
                    sprintf(lcd_buffer, "Selected: %s", gain_buttons[selected_gain_button].text);
                    lcd_show_string(10, 210, lcddev.width, 20, 12, lcd_buffer, BLUE);
                } else {
                    // 切换到频率模式
                    lcd_show_string(10, 190, lcddev.width, 20, 12, "Mode: Frequency Control", BLACK);
                    draw_all_buttons(selected_button);

                    // 显示当前选中的频率按钮信息
                    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);
                }

                delay_ms(500);  // 防抖
            }
        }
        // 检测长按PE4+PE3（超过1秒）- 切换自动增益开关
        else if (KEY0 == 0 && KEY1 == 0) {  // 同时按下PE4和PE3
            static uint32_t auto_gain_switch_counter = 0;
            static uint8_t auto_gain_switch_triggered = 0;

            auto_gain_switch_counter++;

            // 长按超过1秒（100 × 10ms）切换自动增益
            if (auto_gain_switch_counter > 100 && !auto_gain_switch_triggered) {
                auto_gain_switch_triggered = 1;
                auto_gain_enabled = !auto_gain_enabled;

                // 显示自动增益状态
                sprintf(lcd_buffer, "Auto Gain: %s", auto_gain_enabled ? "ENABLED" : "DISABLED");
                lcd_show_string(10, 30, lcddev.width, 20, 16, lcd_buffer, auto_gain_enabled ? GREEN : RED);
                delay_ms(1000);  // 显示1秒
                lcd_fill(10, 30, 320, 50, WHITE);  // 清除提示

                gain_changed = 1;  // 更新显示
            }
        }
        else {
            mode_switch_pressed = 0;
        }

        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0 && KEY1 != 0)  // 只按KEY0，不按KEY1
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                if (gain_mode) {
                    // 增益模式：移动增益按钮选择
                    selected_gain_button = (selected_gain_button + 1) % 4;
                    draw_all_gain_buttons(selected_gain_button);

                    // 显示当前选中的增益按钮信息
                    sprintf(lcd_buffer, "Selected: %s", gain_buttons[selected_gain_button].text);
                    lcd_show_string(10, 210, lcddev.width, 20, 12, lcd_buffer, BLUE);
                } else {
                    // 频率模式：移动频率按钮选择
                    selected_button = (selected_button + 1) % 4;
                    draw_all_buttons(selected_button);

                    // 显示当前选中的按钮信息
                    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);
                }

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0 && KEY0 != 0)  // 只按KEY1，不按KEY0
        {
            if (key1_pressed == 0)  // 首次按下
            {
                key1_pressed = 1;
                key1_press_time = 0;  // 重置计时器
            }
            else
            {
                key1_press_time++;  // 增加按下时间计数
            }

            if (gain_mode) {
                // 增益模式：根据按下时间决定增加还是减少
                if (key1_press_time == 1) {
                    // 短按：增加增益
                    draw_gain_button(&gain_buttons[selected_gain_button], 10 + selected_gain_button * 70, 250, 1, 1);
                    delay_ms(100);  // 显示按下效果

                    float gain_step = gain_buttons[selected_gain_button].gain_step;
                    adjust_ad603_gain(gain_step);

                    // 恢复按钮正常显示
                    draw_all_gain_buttons(selected_gain_button);
                }
                else if (key1_press_time == 50) {  // 长按约500ms后
                    // 长按：减少增益
                    draw_gain_button(&gain_buttons[selected_gain_button], 10 + selected_gain_button * 70, 250, 1, 1);
                    delay_ms(100);  // 显示按下效果

                    float gain_step = -gain_buttons[selected_gain_button].gain_step;  // 负值，减少增益
                    adjust_ad603_gain(gain_step);

                    // 恢复按钮正常显示
                    draw_all_gain_buttons(selected_gain_button);

                    // 显示减少增益的提示
                    sprintf(lcd_buffer, "Decrease: %.2fdB", -gain_step);
                    lcd_show_string(10, 200, lcddev.width, 20, 12, lcd_buffer, RED);
                    delay_ms(500);
                    lcd_fill(10, 200, 320, 220, WHITE);  // 清除提示
                }
            } else {
                    // 频率模式：执行频率调整
                    draw_button(&buttons[selected_button], 1, 1);
                    delay_ms(100);  // 显示按下效果

                    // 执行按钮功能
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);

                    // 恢复按钮正常显示
                    draw_all_buttons(selected_button);
                }

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;      // 按键释放
            key1_press_time = 0;   // 重置按下时间计数器
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志
        }

        // 更新AD603增益显示
        if (gain_changed) {
            // 清除增益显示区域
            lcd_fill(10, 280, 320, 400, WHITE);

            // 显示AD603增益
            float linear_gain = powf(10.0f, current_ad603_gain / 20.0f);
            sprintf(lcd_buffer, "AD603: %.3fdB (x%.3f)", current_ad603_gain, linear_gain);
            lcd_show_string(10, 280, lcddev.width, 20, 12, lcd_buffer, RED);

            // 显示当前频率和校正系数
            float correction_factor = get_frequency_correction_factor(current_frequency);
            sprintf(lcd_buffer, "Freq: %.0fHz  Factor: %.3f", current_frequency, correction_factor);
            lcd_show_string(10, 300, lcddev.width, 20, 12, lcd_buffer, GREEN);

            // 显示目标和实际输出
            float target_output = calculate_target_ad603_output(current_frequency);
            sprintf(lcd_buffer, "Target: %.3fV  Actual: %.3fV", target_output, current_adc_voltage);
            lcd_show_string(10, 320, lcddev.width, 20, 12, lcd_buffer, BLUE);

            // 显示自动增益状态
            sprintf(lcd_buffer, "Auto Gain: %s  Final: %.3fV",
                    auto_gain_enabled ? "ON" : "OFF",
                    current_adc_voltage * 3.9f * correction_factor);
            lcd_show_string(10, 340, lcddev.width, 20, 12, lcd_buffer, MAGENTA);

            // 显示控制提示
            sprintf(lcd_buffer, "Manual: +%.3fdB/-%.3fdB",
                    gain_buttons[selected_gain_button].gain_step,
                    gain_buttons[selected_gain_button].gain_step);
            lcd_show_string(10, 360, lcddev.width, 20, 12, lcd_buffer, GRAY);

            gain_changed = 0;  // 清除改变标志
        }

        // 定期执行自动增益调节（每100ms执行一次）
        static uint32_t auto_gain_counter = 0;
        auto_gain_counter++;
        if (auto_gain_counter >= 10) {  // 10 × 10ms = 100ms
            auto_gain_counter = 0;
            if (auto_gain_enabled) {
                auto_adjust_gain();
            }
        }

        delay_ms(10);  // 主循环延时
    }
}